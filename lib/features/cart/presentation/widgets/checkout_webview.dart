import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../screens/checkout_success_screen.dart';

class CheckoutWebView extends StatefulWidget {
  final String checkoutUrl;
  final VoidCallback? onCheckoutComplete;
  final bool showSuccessScreen;

  const CheckoutWebView({
    super.key,
    required this.checkoutUrl,
    this.onCheckoutComplete,
    this.showSuccessScreen = false,
  });

  @override
  State<CheckoutWebView> createState() => _CheckoutWebViewState();
}

class _CheckoutWebViewState extends State<CheckoutWebView> {
  late final WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading progress if needed
          },
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            debugPrint('[CheckoutWebView] Navigation to: ${request.url}');

            // Option B: Detect custom app deep links (preferred)
            if (request.url.startsWith('myapp://checkout/success')) {
              debugPrint('[CheckoutWebView] Deep link detected: ${request.url}');
              _handleCheckoutSuccess(request.url);
              return NavigationDecision.prevent;
            }

            // Option A: Detect Shopify thank you pages
            if (_isCheckoutComplete(request.url)) {
              debugPrint('[CheckoutWebView] Checkout completed via thank you page: ${request.url}');
              _handleCheckoutSuccess(request.url);
              return NavigationDecision.prevent;
            }

            // Allow external links to open in system browser
            if (_isExternalLink(request.url)) {
              debugPrint('[CheckoutWebView] External link detected, opening in system browser');
              // You could use url_launcher here to open in system browser
              return NavigationDecision.prevent;
            }

            return NavigationDecision.navigate;
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('[CheckoutWebView] Error: ${error.description}');
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.checkoutUrl));
  }

  bool _isCheckoutComplete(String url) {
    // Extract the base domain from the original checkout URL
    final checkoutUri = Uri.tryParse(widget.checkoutUrl);
    final baseDomain = checkoutUri?.host ?? '';

    // Detect various Shopify checkout completion patterns (2025 format)

    // Pattern 1: Redirect to home page after checkout completion
    if (url == 'https://$baseDomain/' || url == 'https://$baseDomain') {
      debugPrint('[CheckoutWebView] Detected redirect to home page - checkout likely completed');
      return true;
    }

    // Pattern 2: Traditional thank you pages
    if (url.contains('/checkout/thank_you') ||
        url.contains('/thank_you') ||
        url.contains('/orders/') ||
        url.contains('order_status_url')) {
      return true;
    }

    // Pattern 3: Order status pages
    if (url.contains('/orders/') && url.contains('status')) {
      return true;
    }

    // Pattern 4: Checkout completion indicators
    if (url.contains('checkout_complete=true') ||
        url.contains('order_id=') ||
        url.contains('order_number=')) {
      return true;
    }

    // Pattern 5: Modern Shopify checkout completion (redirect patterns)
    if (url.contains('/checkouts/') && url.contains('/complete')) {
      return true;
    }

    return false;
  }

  bool _isExternalLink(String url) {
    // Extract the base domain from the original checkout URL
    final checkoutUri = Uri.tryParse(widget.checkoutUrl);
    final baseDomain = checkoutUri?.host ?? '';

    // Allow Shopify-related domains
    if (url.contains('shopify.com') ||
        url.contains('shopifyinc.com') ||
        url.contains('shopifycs.com') ||
        url.contains(baseDomain)) {
      return false;
    }

    // Detect external links that should open in system browser
    return url.startsWith('mailto:') ||
           url.startsWith('tel:') ||
           url.startsWith('sms:') ||
           url.startsWith('http') && !url.contains(baseDomain);
  }

  void _handleCheckoutSuccess(String url) {
    debugPrint('[CheckoutWebView] Checkout success detected from URL: $url');

    // Extract order information if available
    String? orderId = _extractOrderId(url);
    Map<String, String> orderData = _extractOrderData(url);

    if (orderId != null) {
      debugPrint('[CheckoutWebView] Order ID extracted: $orderId');
    }

    if (mounted) {
      if (widget.showSuccessScreen) {
        // Navigate to dedicated success screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => CheckoutSuccessScreen(
              orderId: orderData['order_id'],
              orderNumber: orderData['order_number'],
              total: orderData['total'],
              currency: orderData['currency'],
            ),
          ),
        );
      } else {
        // Call the completion callback which will handle navigation and notification
        if (widget.onCheckoutComplete != null) {
          debugPrint('[CheckoutWebView] Calling onCheckoutComplete - callback will handle navigation and notification');
          widget.onCheckoutComplete!();
        } else {
          // Fallback: show default success message if no callback provided
          debugPrint('[CheckoutWebView] No callback provided, showing default success message');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('🎉 Order completed successfully!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  String? _extractOrderId(String url) {
    // Try to extract order ID from various URL patterns
    final patterns = [
      RegExp(r'order_id=([^&]+)'),
      RegExp(r'orders/([^/?]+)'),
      RegExp(r'order=([^&]+)'),
      RegExp(r'/(\d+)/?$'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(url);
      if (match != null && match.group(1) != null) {
        return match.group(1);
      }
    }

    return null;
  }

  Map<String, String> _extractOrderData(String url) {
    final uri = Uri.tryParse(url);
    final data = <String, String>{};

    if (uri != null) {
      // Extract from query parameters
      data.addAll(uri.queryParameters);

      // Try to extract order ID from path if not in query
      if (!data.containsKey('order_id')) {
        final orderId = _extractOrderId(url);
        if (orderId != null) {
          data['order_id'] = orderId;
        }
      }
    }

    return data;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Your Order'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            _showExitConfirmation();
          },
        ),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
            ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'Loading checkout...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _showExitConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Exit Checkout?'),
          content: const Text(
            'Are you sure you want to exit? Your items will remain in your cart.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Close webview
              },
              child: const Text(
                'Exit',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
