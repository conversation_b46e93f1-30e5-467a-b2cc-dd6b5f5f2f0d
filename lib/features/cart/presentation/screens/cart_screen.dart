import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import '../providers/cart_provider.dart';
import '../providers/cart_state.dart';
import '../widgets/cart_item_card.dart';
import '../widgets/cart_header.dart';
import '../widgets/cart_bottom_info.dart';
import '../widgets/checkout_webview.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';

class CartScreen extends ConsumerStatefulWidget {
  const CartScreen({super.key});

  @override
  ConsumerState<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends ConsumerState<CartScreen> {
  // Track selected items for checkout
  final Set<String> _selectedItems = <String>{};

  @override
  void initState() {
    super.initState();
    // Load cart data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthAndLoadCart();
    });
  }

  Future<void> _checkAuthAndLoadCart() async {
    // Check if user has backend authentication
    final isAuthenticated = await AuthGuardService.requireAuthForCart(
      context,
      ref,
      onSuccess: () {
        // Load cart after successful authentication
        if (mounted) {
          ref.read(cartProvider.notifier).getCart();
        }
      },
    );

    // If already authenticated, load cart directly
    if (isAuthenticated && mounted) {
      ref.read(cartProvider.notifier).getCart();
    }
  }

  @override
  Widget build(BuildContext context) {
    final cartState = ref.watch(cartProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFF3F4F6),
      appBar: const CartHeader(),
      body: Column(
        children: [
          // Cart Content
          Expanded(
            child: _buildCartContent(cartState),
          ),

          // Only show bottom info when cart is loaded and has items
          if (cartState.status == CartStatus.loaded &&
              cartState.cart != null &&
              cartState.cart!.items.isNotEmpty)
            CartBottomInfo(
              isAllSelected: _isAllSelected(cartState.cart!),
              onSelectAllChanged: _handleSelectAllChanged,
              totalPrice: _calculateSelectedTotal(cartState.cart!),
              onCheckout: _handleCheckout,
            ),
        ],
      ),
    );
  }

  Widget _buildCartContent(CartState cartState) {
    switch (cartState.status) {
      case CartStatus.initial:
      case CartStatus.loading:
        return const Center(
          child: CircularProgressIndicator(
            color: Color(0xFFFF6982),
          ),
        );
      
      case CartStatus.error:
        return _buildErrorState(cartState.error);
      
      case CartStatus.loaded:
        if (cartState.cart == null || cartState.cart!.items.isEmpty) {
          return _buildEmptyCart();
        }
        return _buildCartItems(cartState.cart!);
    }
  }

  Widget _buildErrorState(String? error) {
    final cartState = ref.watch(cartProvider);
    final isAuthError = !cartState.isAuthenticated ||
                       (error != null && (error.contains('Backend authentication required') ||
                                         error.contains('Authentication required') ||
                                         error.contains('Unauthorized')));

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isAuthError ? Icons.lock_outline : Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            isAuthError ? 'Authentication Required' : 'Unable to load cart',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isAuthError
              ? 'Please sign in to access your cart'
              : error ?? 'Failed to load cart',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: const Color(0xFF9CA3AF),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: isAuthError ? _handleAuthRequired : () => ref.read(cartProvider.notifier).getCart(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF6982),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              isAuthError ? 'Sign In' : 'Try Again',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleAuthRequired() async {
    await AuthGuardService.requireAuthForCart(
      context,
      ref,
      onSuccess: () {
        // Refresh cart after successful authentication
        debugPrint('[CartScreen] Authentication successful - user can now access cart');
        if (mounted) {
          ref.read(cartProvider.notifier).getCart();
        }
      },
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Cart is empty',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add some products to get started',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: const Color(0xFF9CA3AF),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.go('/home'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF6982),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Start Shopping',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCartItems(cart) {
    // Initialize selected items when cart loads
    if (_selectedItems.isEmpty && cart.items.isNotEmpty) {
      for (final item in cart.items) {
        _selectedItems.add(item.variantId);
      }
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: cart.items.length,
      itemBuilder: (context, index) {
        final item = cart.items[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: CartItemCard(
            item: item,
            isSelected: _selectedItems.contains(item.variantId),
            onSelectionChanged: (isSelected) => _handleSelectionChanged(item.variantId, isSelected),
            onQuantityChanged: (newQuantity) => _handleQuantityChange(item, newQuantity),
            onRemove: () => _handleRemoveItem(item),
          ),
        );
      },
    );
  }

  void _handleSelectionChanged(String variantId, bool isSelected) {
    setState(() {
      if (isSelected) {
        _selectedItems.add(variantId);
      } else {
        _selectedItems.remove(variantId);
      }
    });
  }

  bool _isAllSelected(cart) {
    if (cart.items.isEmpty) return false;
    return cart.items.every((item) => _selectedItems.contains(item.variantId));
  }

  void _handleSelectAllChanged(bool selectAll) {
    final cartState = ref.read(cartProvider);
    if (cartState.cart == null) return;

    setState(() {
      if (selectAll) {
        _selectedItems.addAll(cartState.cart!.items.map((item) => item.variantId));
      } else {
        _selectedItems.clear();
      }
    });
  }

  double _calculateSelectedTotal(cart) {
    double total = 0;
    debugPrint('🧮 Calculating selected total:');
    for (final item in cart.items) {
      if (_selectedItems.contains(item.variantId)) {
        final itemTotal = item.price * item.quantity;
        debugPrint('🧮 Item: ${item.title}');
        debugPrint('🧮 Unit price: ${item.price}, Quantity: ${item.quantity}, Item total: $itemTotal');
        total += itemTotal;
      }
    }
    debugPrint('🧮 Final selected total: $total');
    return total;
  }

  void _handleQuantityChange(item, int newQuantity) {
    if (newQuantity <= 0) {
      _handleRemoveItem(item);
    } else {
      ref.read(cartProvider.notifier).updateItem(
        variantId: item.variantId,
        quantity: newQuantity,
      );
    }
  }

  void _handleRemoveItem(item) {
    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Remove Item',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            'Are you sure you want to remove "${item.title}" from your cart?',
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _selectedItems.remove(item.variantId);
                });
                ref.read(cartProvider.notifier).removeItem(
                  variantId: item.variantId,
                );

                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Item removed from cart'),
                    backgroundColor: Colors.green,
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
              child: Text(
                'Remove',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _handleCheckout() async {
    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              SizedBox(width: 12),
              Text('Processing checkout...'),
            ],
          ),
          backgroundColor: const Color(0xFFFF6982),
          duration: Duration(seconds: 30), // Longer duration for processing
        ),
      );

      final checkoutResult = await ref.read(cartProvider.notifier).checkout();

      // Hide loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }

      if (checkoutResult != null && mounted) {
        // Parse the checkout result to extract URL
        try {
          final Map<String, dynamic> result = json.decode(checkoutResult);
          if (result['success'] == true && result['data']?['checkout_url'] != null) {
            final checkoutUrl = result['data']['checkout_url'] as String;

            // Navigate to checkout WebView
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => CheckoutWebView(
                  checkoutUrl: checkoutUrl,
                  onCheckoutComplete: () {
                    debugPrint('[CartScreen] Checkout completed, navigating to home first');

                    // Refresh cart after successful checkout
                    ref.read(cartProvider.notifier).getCart();

                    // Navigate to home screen first
                    if (mounted) {
                      final router = GoRouter.of(context);
                      final messenger = ScaffoldMessenger.of(context);
                      router.go('/');

                      // Show success notification after navigation to home
                      Future.delayed(const Duration(milliseconds: 800), () {
                        if (mounted) {
                          debugPrint('[CartScreen] Showing success notification on home screen');
                          messenger.showSnackBar(
                            SnackBar(
                              content: Text('🎉 Order completed successfully!'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 3),
                            ),
                          );
                        }
                      });
                    }
                  },
                ),
              ),
            );
          } else {
            // Show error message from backend
            final message = result['message'] ?? 'Checkout failed';
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message),
                backgroundColor: Colors.red,
              ),
            );
          }
        } catch (e) {
          // Fallback: treat as simple success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(checkoutResult),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      } else if (mounted) {
        // Checkout failed
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Checkout Failed',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 4),
                Text(
                  'There was an issue processing your checkout. This is likely a backend configuration issue with Shopify.',
                  style: TextStyle(fontSize: 12),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () => _handleCheckout(),
            ),
          ),
        );
      }
    } catch (e) {
      // Hide loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Checkout Error',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 4),
                Text(
                  'Error: ${e.toString()}',
                  style: TextStyle(fontSize: 12),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
    }
  }


}
