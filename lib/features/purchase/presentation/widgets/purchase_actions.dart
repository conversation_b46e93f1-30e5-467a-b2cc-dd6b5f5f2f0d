import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../../../auth/presentation/services/auth_guard_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/data/models/enhanced_user_model.dart';
import '../providers/buy_now_provider.dart';
import 'buy_now_checkout_webview.dart';

class PurchaseActions extends ConsumerStatefulWidget {
  final VoidCallback onAddToCart;
  final VoidCallback? onBuyNow;
  final String? variantId;
  final String? productId;
  final int? quantity;
  final double? price;
  final String? title;
  final VoidCallback? onCloseModal; // Callback to close parent modal

  const PurchaseActions({
    super.key,
    required this.onAddToCart,
    this.onBuyNow,
    this.variantId,
    this.productId,
    this.quantity,
    this.price,
    this.title,
    this.onCloseModal,
  });

  @override
  ConsumerState<PurchaseActions> createState() => _PurchaseActionsState();
}

class _PurchaseActionsState extends ConsumerState<PurchaseActions> {
  DateTime? _lastAddToCartClick;
  DateTime? _lastBuyNowClick;
  static const Duration _debounceDelay = Duration(seconds: 2);
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Add to Cart button
        Expanded(
          child: OutlinedButton(
            onPressed: () => _handleAddToCart(context, ref),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: Color(0xFFFF6982), width: 1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(vertical: 14),
            ),
            child: Text(
              'Add to Cart',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFFFF6982),
                height: 1.43,
              ),
            ),
          ),
        ),

        const SizedBox(width: 12),

        // Buy Now button
        Expanded(
          child: ElevatedButton(
            onPressed: () => _handleBuyNow(context, ref),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF6982),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(vertical: 14),
              elevation: 0,
            ),
            child: Text(
              'Buy now',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
                height: 1.43,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleAddToCart(BuildContext context, WidgetRef ref) async {
    // Prevent spam clicking with time-based debounce
    final now = DateTime.now();
    if (_lastAddToCartClick != null &&
        now.difference(_lastAddToCartClick!) < _debounceDelay) {
      debugPrint('[PurchaseActions] Add to Cart clicked too soon, ignoring (debounce)');
      return;
    }
    _lastAddToCartClick = now;

    debugPrint('[PurchaseActions] Add to Cart button clicked');

    // Check if user is already authenticated
    final authState = ref.read(authNotifierProvider);
    final isAuthenticated = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (isAuthenticated) {
      debugPrint('[PurchaseActions] User already authenticated, executing Add to Cart');
      // User is already authenticated, execute immediately
      widget.onAddToCart();
    } else {
      debugPrint('[PurchaseActions] User not authenticated, showing auth modal');
      // User needs authentication, show modal but keep it open after auth
      await AuthGuardService.requireAuthForCart(
        context,
        ref,
        onSuccess: () {
          debugPrint('[PurchaseActions] Authentication successful for Add to Cart');
          debugPrint('[PurchaseActions] Modal remains open - user can now click Add to Cart when ready');
          // Don't auto-execute - keep modal open for user to click when ready
          debugPrint('[PurchaseActions] User is now authenticated and can proceed manually');
        },
        onError: (errorMessage) {
          debugPrint('[PurchaseActions] *** ERROR CALLBACK TRIGGERED *** Authentication failed for Add to Cart: $errorMessage');
          // Show error in the purchase modal context so it persists
          try {
            SnackbarUtils.showAuthError(context, errorMessage);
            debugPrint('[PurchaseActions] ✅ Error SnackBar shown successfully');
          } catch (e) {
            debugPrint('[PurchaseActions] ❌ Failed to show error SnackBar: $e');
            // Fallback: try with root context
            try {
              final rootContext = Navigator.of(context, rootNavigator: true).context;
              SnackbarUtils.showAuthError(rootContext, errorMessage);
              debugPrint('[PurchaseActions] ✅ Error SnackBar shown with root context');
            } catch (e2) {
              debugPrint('[PurchaseActions] ❌ Failed to show error SnackBar with root context: $e2');
            }
          }
        },
      );

      // Check authentication state after modal returns
      final authStateAfter = ref.read(authNotifierProvider);
      final isAuthenticatedAfter = authStateAfter.maybeMap(
        authenticated: (state) {
          if (state.user is EnhancedUserModel) {
            final enhancedUser = state.user as EnhancedUserModel;
            return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
          }
          return false;
        },
        error: (errorState) {
          debugPrint('[PurchaseActions] Authentication failed: ${errorState.failure?.message}');
          return false;
        },
        orElse: () => false,
      );

      if (!isAuthenticatedAfter) {
        debugPrint('[PurchaseActions] User returned from auth modal but is still not authenticated');
        debugPrint('[PurchaseActions] Modal remains open - user can try authentication again or modify selection');
      }
    }
  }

  Future<void> _handleBuyNow(BuildContext context, WidgetRef ref) async {
    // Prevent spam clicking with time-based debounce
    final now = DateTime.now();
    if (_lastBuyNowClick != null &&
        now.difference(_lastBuyNowClick!) < _debounceDelay) {
      debugPrint('[PurchaseActions] Buy Now clicked too soon, ignoring (debounce)');
      return;
    }
    _lastBuyNowClick = now;

    debugPrint('[PurchaseActions] Buy Now button clicked');
    debugPrint('[PurchaseActions] variantId: ${widget.variantId}, quantity: ${widget.quantity}');

    // Check if user is already authenticated
    final authState = ref.read(authNotifierProvider);
    final isAuthenticated = authState.maybeMap(
      authenticated: (state) {
        if (state.user is EnhancedUserModel) {
          final enhancedUser = state.user as EnhancedUserModel;
          return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
        }
        return false;
      },
      orElse: () => false,
    );

    if (isAuthenticated) {
      debugPrint('[PurchaseActions] User already authenticated, executing Buy Now');
      // User is already authenticated, execute immediately
      if (widget.variantId != null && widget.quantity != null) {
        await _executeBuyNow(context, ref);
      } else if (widget.onBuyNow != null) {
        widget.onBuyNow!();
      }
    } else {
      debugPrint('[PurchaseActions] User not authenticated, showing auth modal');
      // User needs authentication, show modal but keep it open after auth
      await AuthGuardService.requireAuthForPurchase(
        context,
        ref,
        onSuccess: () {
          debugPrint('[PurchaseActions] Authentication successful for Buy Now');
          debugPrint('[PurchaseActions] Modal remains open - user can now click Buy Now when ready');
          // Don't auto-execute - keep modal open for user to click when ready
          debugPrint('[PurchaseActions] User is now authenticated and can proceed manually');
        },
        onError: (errorMessage) {
          debugPrint('[PurchaseActions] *** ERROR CALLBACK TRIGGERED *** Authentication failed for Buy Now: $errorMessage');
          // Show error in the purchase modal context so it persists
          try {
            SnackbarUtils.showAuthError(context, errorMessage);
            debugPrint('[PurchaseActions] ✅ Error SnackBar shown successfully');
          } catch (e) {
            debugPrint('[PurchaseActions] ❌ Failed to show error SnackBar: $e');
            // Fallback: try with root context
            try {
              final rootContext = Navigator.of(context, rootNavigator: true).context;
              SnackbarUtils.showAuthError(rootContext, errorMessage);
              debugPrint('[PurchaseActions] ✅ Error SnackBar shown with root context');
            } catch (e2) {
              debugPrint('[PurchaseActions] ❌ Failed to show error SnackBar with root context: $e2');
            }
          }
        },
      );

      // Check authentication state after modal returns
      final authStateAfter = ref.read(authNotifierProvider);
      final isAuthenticatedAfter = authStateAfter.maybeMap(
        authenticated: (state) {
          if (state.user is EnhancedUserModel) {
            final enhancedUser = state.user as EnhancedUserModel;
            return enhancedUser.hasBackendAuth && enhancedUser.backendToken != null && enhancedUser.backendToken!.isNotEmpty;
          }
          return false;
        },
        error: (errorState) {
          debugPrint('[PurchaseActions] Authentication failed: ${errorState.failure?.message}');
          return false;
        },
        orElse: () => false,
      );

      if (!isAuthenticatedAfter) {
        debugPrint('[PurchaseActions] User returned from auth modal but is still not authenticated');
        debugPrint('[PurchaseActions] Modal remains open - user can try authentication again or modify selection');
      }
    }
  }

  /// Execute Buy Now while keeping the modal open
  Future<void> _executeBuyNow(BuildContext context, WidgetRef ref) async {
    try {
      debugPrint('[PurchaseActions] Starting Buy Now execution - modal stays open');

      // Reset buy now state before starting new operation
      ref.read(buyNowProvider.notifier).reset();

      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Execute Buy Now API call
      await ref.read(buyNowProvider.notifier).buyNow(
        variantId: widget.variantId!,
        quantity: widget.quantity!,
        productId: widget.productId,
        price: widget.price,
        title: widget.title,
      );

      // Hide loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Small delay to ensure loading dialog is fully dismissed
      await Future.delayed(const Duration(milliseconds: 100));

      // Get the updated state after the API call
      final buyNowState = ref.read(buyNowProvider);
      debugPrint('[PurchaseActions] Buy Now state after API call: ${buyNowState.status}');

      if (buyNowState.status == BuyNowStatus.success && buyNowState.response != null) {
        debugPrint('[PurchaseActions] Buy Now successful - now closing modal and navigating to checkout');

        final response = buyNowState.response!;
        debugPrint('[PurchaseActions] Checkout URL: ${response.checkoutUrl}');
        debugPrint('[PurchaseActions] Order ID: ${response.orderId}');
        debugPrint('[PurchaseActions] Total: ${response.totalPrice} ${response.currency}');

        // Validate checkout URL before proceeding
        if (response.checkoutUrl.isEmpty) {
          debugPrint('[PurchaseActions] Error: Empty checkout URL received');
          if (context.mounted) {
            SnackbarUtils.showBuyNowError(
              context,
              'Invalid checkout URL received. Please try again.',
              onRetry: () => _executeBuyNow(context, ref),
            );
          }
          return;
        }

        // Additional URL validation
        final uri = Uri.tryParse(response.checkoutUrl);
        if (uri == null) {
          debugPrint('[PurchaseActions] Error: Invalid checkout URL format: ${response.checkoutUrl}');
          if (context.mounted) {
            SnackbarUtils.showBuyNowError(
              context,
              'Invalid checkout URL format. Please try again.',
              onRetry: () => _executeBuyNow(context, ref),
            );
          }
          return;
        }

        debugPrint('[PurchaseActions] Checkout URL validation passed');
        debugPrint('[PurchaseActions] URL scheme: ${uri.scheme}');
        debugPrint('[PurchaseActions] URL host: ${uri.host}');
        debugPrint('[PurchaseActions] URL path: ${uri.path}');

        // Navigate FIRST, then close the modal to avoid context disposal issues
        if (context.mounted) {
          debugPrint('[PurchaseActions] Navigating to checkout WebView BEFORE closing modal');
          debugPrint('[PurchaseActions] Using SimpleCheckoutWebView (no aggressive completion detection)');

          try {

            // Use buy now checkout WebView with comprehensive completion detection
            debugPrint('[PurchaseActions] Using BuyNowCheckoutWebView with robust completion detection');

            final route = MaterialPageRoute(
              builder: (context) => BuyNowCheckoutWebView(
                checkoutUrl: response.checkoutUrl,
                onCloseModal: widget.onCloseModal, // Pass modal close callback
                onCheckoutComplete: () {
                  debugPrint('[PurchaseActions] 🎉 BUY NOW CHECKOUT COMPLETED! This should only happen when user finishes payment!');
                  debugPrint('[PurchaseActions] Using same success handling as cart checkout - navigate to home first');

                  // Capture root context before navigation to avoid disposal issues
                  final rootContext = Navigator.of(context, rootNavigator: true).context;
                  final rootMessenger = ScaffoldMessenger.of(rootContext);
                  final router = GoRouter.of(rootContext);

                  // Navigate to home first (same as cart checkout)
                  Navigator.of(context).popUntil((route) => route.isFirst);
                  router.go('/');

                  // Show success notification after navigation to home using root context
                  Future.delayed(const Duration(milliseconds: 800), () {
                    debugPrint('[PurchaseActions] Attempting to show success notification on home screen');
                    debugPrint('[PurchaseActions] Root context mounted: ${rootContext.mounted}');
                    try {
                      rootMessenger.showSnackBar(
                        const SnackBar(
                          content: Text('🎉 Order completed successfully!'),
                          backgroundColor: Colors.green,
                          duration: Duration(seconds: 3),
                        ),
                      );
                      debugPrint('[PurchaseActions] ✅ Success SnackBar shown successfully');
                    } catch (e) {
                      debugPrint('[PurchaseActions] ❌ Failed to show success SnackBar: $e');
                    }
                  });
                },
                onCheckoutError: (errorMessage) {
                  debugPrint('[PurchaseActions] Buy Now checkout error: $errorMessage');

                  // Capture root context before navigation to avoid disposal issues
                  final rootContext = Navigator.of(context, rootNavigator: true).context;
                  final rootMessenger = ScaffoldMessenger.of(rootContext);
                  final router = GoRouter.of(rootContext);

                  // Navigate to home first, then show error notification
                  Navigator.of(context).popUntil((route) => route.isFirst);
                  router.go('/');

                  // Show error notification after navigation to home using root context
                  Future.delayed(const Duration(milliseconds: 800), () {
                    debugPrint('[PurchaseActions] Attempting to show error notification on home screen');
                    debugPrint('[PurchaseActions] Root context mounted: ${rootContext.mounted}');
                    try {
                      rootMessenger.showSnackBar(
                        SnackBar(
                          content: Text('❌ Buy Now failed: $errorMessage'),
                          backgroundColor: Colors.red,
                          duration: Duration(seconds: 5),
                          action: SnackBarAction(
                            label: 'Retry',
                            textColor: Colors.white,
                            onPressed: () => _executeBuyNow(rootContext, ref),
                          ),
                        ),
                      );
                      debugPrint('[PurchaseActions] ✅ Error SnackBar shown successfully');
                    } catch (e) {
                      debugPrint('[PurchaseActions] ❌ Failed to show error SnackBar: $e');
                    }
                  });
                },
              ),
            );

            debugPrint('[PurchaseActions] BuyNowCheckoutWebView route created, pushing to navigator');
            final result = Navigator.of(context).push(route);
            debugPrint('[PurchaseActions] BuyNowCheckoutWebView navigation initiated successfully');

            // Log when navigation completes
            result.then((value) {
              debugPrint('[PurchaseActions] Navigation completed with result: $value');
            }).catchError((error) {
              debugPrint('[PurchaseActions] Navigation error: $error');
            });

            // DON'T close the modal for buy now - let checkout WebView handle its own lifecycle
            debugPrint('[PurchaseActions] Buy now checkout: Modal stays open, WebView handles its own lifecycle');
            debugPrint('[PurchaseActions] This prevents WebView disposal and auto-closing issues');

          } catch (e) {
            debugPrint('[PurchaseActions] Error during checkout navigation: $e');

            // If navigation fails, still close the modal
            if (widget.onCloseModal != null) {
              widget.onCloseModal!();
            }

            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to open checkout. Please try again.'),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 3),
              ),
            );
          }
        } else {
          debugPrint('[PurchaseActions] Context not mounted, cannot navigate');
          // Close modal even if navigation fails
          if (widget.onCloseModal != null) {
            widget.onCloseModal!();
          }
        }
      } else if (buyNowState.status == BuyNowStatus.error) {
        debugPrint('[PurchaseActions] Buy Now failed: ${buyNowState.errorMessage}');
        // Close modal first, then show error message in parent context
        if (context.mounted) {
          debugPrint('[PurchaseActions] Showing error SnackBar for: ${buyNowState.errorMessage}');

          // Capture context and error message before closing modal
          final rootContext = Navigator.of(context, rootNavigator: true).context;
          final errorMessage = buyNowState.errorMessage ?? 'Unknown error occurred';

          // Close the parent modal since Buy Now failed
          if (widget.onCloseModal != null) {
            debugPrint('[PurchaseActions] Closing parent modal due to Buy Now error');
            widget.onCloseModal!();

            // Show error immediately in parent context
            try {
              SnackbarUtils.showBuyNowError(
                rootContext,
                errorMessage,
                onRetry: null, // Remove retry since modal is closed
              );
              debugPrint('[PurchaseActions] ✅ SnackBar shown via SnackbarUtils with root context');
            } catch (e) {
              debugPrint('[PurchaseActions] ❌ SnackbarUtils with root context failed: $e');
              // Fallback to direct SnackBar
              try {
                ScaffoldMessenger.of(rootContext).showSnackBar(
                  SnackBar(
                    content: Text('Buy Now Failed: $errorMessage'),
                    backgroundColor: Colors.red,
                    duration: Duration(seconds: 5),
                  ),
                );
                debugPrint('[PurchaseActions] ✅ SnackBar shown via direct ScaffoldMessenger');
              } catch (e2) {
                debugPrint('[PurchaseActions] ❌ All SnackBar approaches failed: $e2');
              }
            }
          } else {
            // No modal to close, show error directly
            try {
              SnackbarUtils.showBuyNowError(
                rootContext,
                errorMessage,
                onRetry: () => _executeBuyNow(context, ref),
              );
              debugPrint('[PurchaseActions] ✅ SnackBar shown directly (no modal to close)');
            } catch (e) {
              debugPrint('[PurchaseActions] ❌ Direct SnackBar failed: $e');
            }
          }
        }
      } else {
        debugPrint('[PurchaseActions] Buy Now state is: ${buyNowState.status}');
        // Show generic error for unexpected states
        if (context.mounted) {
          SnackbarUtils.showBuyNowError(
            context,
            'Buy Now failed - unexpected error',
            onRetry: () => _executeBuyNow(context, ref),
          );
        }
      }
    } catch (e) {
      debugPrint('[PurchaseActions] Buy Now exception: $e');
      // Hide loading indicator if still showing, but be careful about context
      if (context.mounted) {
        try {
          Navigator.of(context).pop(); // Try to close loading dialog
        } catch (navError) {
          debugPrint('[PurchaseActions] Could not close loading dialog: $navError');
        }

        // Capture context before closing modal
        final rootContext = Navigator.of(context, rootNavigator: true).context;

        // Close the parent modal since Buy Now failed with exception
        if (widget.onCloseModal != null) {
          debugPrint('[PurchaseActions] Closing parent modal due to Buy Now exception');
          widget.onCloseModal!();

          // Show error immediately in parent context
          try {
            SnackbarUtils.showBuyNowError(
              rootContext,
              'An unexpected error occurred. Please try again.',
              onRetry: null, // Remove retry since modal is closed
            );
            debugPrint('[PurchaseActions] Exception SnackBar shown');
          } catch (snackError) {
            debugPrint('[PurchaseActions] Could not show exception SnackBar: $snackError');
          }
        } else {
          // No modal to close, show error directly
          try {
            SnackbarUtils.showBuyNowError(
              rootContext,
              'An unexpected error occurred. Please try again.',
              onRetry: () => _executeBuyNow(context, ref),
            );
            debugPrint('[PurchaseActions] Exception SnackBar shown directly');
          } catch (snackError) {
            debugPrint('[PurchaseActions] Could not show exception SnackBar: $snackError');
          }
        }
      }
    }
  }
}
